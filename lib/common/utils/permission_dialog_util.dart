import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum PermissionType {
  camera,
  microphone,
  photo,
}

extension PermissionTypeExtension on PermissionType {
  Image get image {
    switch (this) {
      case PermissionType.camera:
        return Image.asset(
          "images/permission_dialog_camera.png",
          width: 88.w,
          height: 88.w,
        );
      case PermissionType.microphone:
        return Image.asset("images/permission_dialog_microphone.png");
      case PermissionType.photo:
        return Image.asset("images/permission_dialog_photo.png");
    }
  }

  String get title {
    switch (this) {
      case PermissionType.camera:
        return "相机使用权限";
      case PermissionType.microphone:
        return "麦克风使用权限";
      case PermissionType.photo:
        return "相册使用权限";
    }
  }
}

class PermissionData {
  final PermissionType type;
  final String description;

  PermissionData({
    required this.type,
    required this.description,
  });
}

class PermissionDialogUtil {
  static Future<bool?> show(
      BuildContext context, List<PermissionData> permissionDataList) {
    if (Platform.isIOS) {
      //只有安卓弹框
      return Future.value(true);
    }
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
            backgroundColor: Colors.white,
            elevation: 0,
            // contentPadding: EdgeInsets.all(30.w),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(40.w),
            ),
            title: Text(
              "小西口语向您申请以下权限",
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontSize: 36.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF272733)),
            ),
            contentPadding: EdgeInsets.only(
                bottom: 32.w, top: 32.w, left: 32.w, right: 32.w),
            content: Container(
              padding: EdgeInsets.only(bottom: 24.w, left: 32.w, right: 32.w),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(32.w),
                  color: const Color(0xFFF5F9FA)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  ...permissionDataList.map((permission) => Padding(
                        padding: EdgeInsets.only(bottom: 8.w, top: 32.w),
                        child: _buildCameraPermissionItem(context, permission),
                      )),
                ],
              ),
            ),
            actions: [
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop(false);
                        },
                        child: Container(
                          height: 102.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.w),
                            border: Border.all(
                                color: const Color(0xFFCEDDE0), width: 1.w),
                          ),
                          child: Center(
                            child: Text("拒绝", style: style_1_36_500),
                          ),
                        )),
                  ),
                  SizedBox(width: 20.w),
                  Expanded(
                    child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop(true);
                        },
                        child: Container(
                          height: 102.w,
                          decoration: BoxDecoration(
                            color: const Color(0xFF2693FF),
                            borderRadius: BorderRadius.circular(24.w),
                          ),
                          child: Center(
                            child: Text("去开启",
                                style: style_1_36_500.copyWith(
                                    color: Colors.white)),
                          ),
                        )),
                  ),
                ],
              )
            ]);
      },
    );
  }

  //相机拍照widget，左侧是头像，右侧是标题和权限描述
  static Widget _buildCameraPermissionItem(
      BuildContext context, PermissionData permission) {
    return Row(
      children: [
        _buildPermissionIcon(permission),
        SizedBox(width: 24.w),
        _buildPermissionText(permission),
      ],
    );
  }

  static Widget _buildPermissionIcon(PermissionData permission) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(44.w),
        color: const Color(0xFFF5F9FA),
      ),
      child: permission.type.image,
    );
  }

  static Widget _buildPermissionText(PermissionData permission) {
    return Expanded(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        permission.type.title,
        style: TextStyle(
            fontSize: 32.sp,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF272733)),
      ),
      SizedBox(height: 10.w),
      Text(
        permission.description,
        style: TextStyle(fontSize: 24.sp, color: const Color(0xFF4E5766)),
      ),
    ]));
  }
}
