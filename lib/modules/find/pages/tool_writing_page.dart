import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/models/tool_user_writing_page_model/recods.dart';
import 'package:flutter_app_kouyu/common/style_util.dart';
import 'package:flutter_app_kouyu/common/utils/color_util.dart';
import 'package:flutter_app_kouyu/common/utils/permission_dialog_util.dart';
import 'package:flutter_app_kouyu/modules/find/pages/find_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_photo_ocr_page.dart';
import 'package:flutter_app_kouyu/modules/find/pages/tool_writing_detail_page.dart';
import 'package:flutter_app_kouyu/modules/find/provider/tool_user_writing_page_provider.dart';
import 'package:flutter_app_kouyu/modules/ielts/pages/ielt_prescription_page.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/overlay_manager.dart';
import 'package:flutter_app_kouyu/widgets/vip_spical_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class ToolWritingPage extends StatefulWidget {
  String inputText = '';

  @override
  _ToolWritingPageState createState() => _ToolWritingPageState();

  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => ToolWritingPage());
  }
}

class _ToolWritingPageState extends State<ToolWritingPage> {
  final ImagePicker picker = ImagePicker();
  late TextEditingController inputTextController =
      TextEditingController(text: widget.inputText);

  int _textLength = 0;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ToolUserWritingPageProvider>(
      create: (_) => ToolUserWritingPageProvider()
        ..loadData()
        ..checkIsVip()
        ..tryTimes(),
      child: Consumer<ToolUserWritingPageProvider>(
        builder: (context, model, child) {
          return Stack(children: [
            Container(
              width: double.infinity,
              height: double.infinity,
              // color: const Color(0xFFF3FBFD),
              color: const Color(0xFFE9F5F7),
              // color: const Color(0xFFE9F5F7),
            ),
            Image.asset(
              "images/my_bg.png",
              width: double.infinity,
            ),
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                leadingWidth: 300,
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: CustomAppbar.leftWidget(context, text: "写作批改"),
              ),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerDocked,
              body: GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                },
                child: Padding(
                    padding: EdgeInsets.only(top: 32.w),
                    child: SmartRefresher(
                      controller: model.refreshController,
                      enablePullUp: true,
                      enablePullDown: false,
                      onLoading: () {
                        model.loadMore();
                      },
                      child: CustomScrollView(slivers: [
                        SliverToBoxAdapter(
                            child: _wirtingWidget(context, model)),
                        SliverToBoxAdapter(
                            child: _centerButtonWidget(context, model)),
                        SliverToBoxAdapter(
                            child: _centerTryTimesWidget(context, model)),
                        SliverPersistentHeader(
                            // pinned: true,
                            delegate: SliverHeaderDelegate(
                          height: 96.w,
                          child: Container(
                              // width: 670.w,
                              margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
                              child: Text(
                                "批改记录",
                                style: style_1_24,
                              )),
                        )),
                        SliverList.separated(
                          itemBuilder: (_, index) {
                            Recods? recods = model
                                .toolUserWritingPageModel?.data?.recods![index];
                            return _listItemWidget(context, recods!);
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              height: 16.w,
                            );
                          },
                          itemCount: model.toolUserWritingPageModel?.data
                                  ?.recods?.length ??
                              0,
                        ),
                      ]),
                    )),
              ),
            ),
          ]);
        },
      ),
    );
  }

  Widget _wirtingWidget(
      BuildContext context, ToolUserWritingPageProvider provider) {
    return Container(
        width: 325.w,
        height: 425.w,
        padding: EdgeInsets.all(32.2.w),
        margin: EdgeInsets.fromLTRB(32.2.w, 32.2.w, 32.2.w, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Color(0xFF84E9FF)),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Column(
          children: [
            Expanded(
              child: TextField(
                maxLength: 3000, // 设置最大字数为1000
                cursorColor: Color(0xFF84E9FF),
                maxLines: 6,
                decoration: InputDecoration(
                  // contentPadding: EdgeInsets.symmetric(vertical: 200.w),
                  filled: true,
                  fillColor: Colors.white,
                  hintText: "请粘贴或输入文章内容",
                  border: InputBorder.none,
                  counterText: "",
                ),
                controller: inputTextController,

                onChanged: (value) {
                  widget.inputText = value;
                  setState(() {
                    _textLength = value.length;
                  });
                  // print(controller.text.length);
                },
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                    "${inputTextController.text.length}/${provider.maxLengthInput}",
                    style: style_1_24),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    int num = provider.toolUserTryTimesModel?.data
                            ?.toolTryTimesCritiqueOfWriting ??
                        0;

                    bool isVip = provider.isVip;
                    if (num <= 0 && !isVip) {
                      //体验到期
                      OverlayManager.getInstance().showOverlay(
                        true,
                        builder: (close) => VipSpicalWidget(
                          close: close,
                        ),
                      );
                      return;
                    }

                    Navigator.of(context)
                        .push(
                      MaterialPageRoute(
                        builder: (context) => ToolPhotoOcrPage(
                          toolType: ToolType.writing,
                          cbParam: provider.isVip ? 'vip' : '',
                        ),
                      ),
                    )
                        .then((result) {
                      if (result != null) {
                        print(result);
                        setState(() {
                          inputTextController.text = result['result'];
                          _textLength = result['result'].length;
                        });
                      }
                    });
                  },
                  child: Image.asset(
                    "images/camera-line.png",
                    width: 50.w,
                    height: 50.w,
                  ),
                )
              ],
            ),
          ],
        ));
  }

  Widget _centerButtonWidget(
      BuildContext context, ToolUserWritingPageProvider provider) {
    return GestureDetector(
      onTap: () async {
        int num = provider
                .toolUserTryTimesModel?.data?.toolTryTimesCritiqueOfWriting ??
            0;

        bool isVip = provider.isVip;
        if (num <= 0 && !isVip) {
          //体验到期
          OverlayManager.getInstance().showOverlay(
            true,
            builder: (close) => VipSpicalWidget(
              close: close,
            ),
          );
          return;
        }
        provider.inputText = inputTextController.text;

        if (await provider.compositionAssessment(provider.inputText)) {
          Navigator.of(context).push(ToolWritingDetailPage.route(
              provider.toolUserWritingCompositionAssessmentModel!.data?.id ??
                  0));
        }
      },
      child: Container(
        width: 670.w,
        height: 102.w,
        margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w),
          color: const Color(0xFF2693FF),
        ),
        child: Center(
            child: Text(
          "一键批改",
          style: style_1_32.copyWith(color: Colors.white),
        )),
      ),
    );
  }

  Widget _centerTryTimesWidget(
      BuildContext context, ToolUserWritingPageProvider model) {
    // bool isVip = provider.checkIsVip();

    int num =
        model.toolUserTryTimesModel?.data?.toolTryTimesCritiqueOfWriting ?? 0;
    if (!model.isVip)
      return Container(
          width: 670.w,
          margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "剩余$num次免费使用，",
                style: style_1_24,
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pushNamed('/open_vip');
                },
                child: Text(
                  "开通学习会员畅快使用",
                  style: style_bue_24,
                ),
              ),
            ],
          ));

    return Container(
        width: 670.w,
        margin: EdgeInsets.fromLTRB(24.w, 24.w, 24.w, 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "已是会员，享受无限次免费使用",
              style: style_1_24,
            )
          ],
        ));
  }

  Container _lineWidget() {
    return Container(
      height: 3.w,
      width: double.infinity,
      color: ColorUtil.separated,
    );
  }

  Widget _listItemWidget(BuildContext context, Recods recod) {
    // IeltsListProvider provider = context.read<IeltsListProvider>();

    return GestureDetector(
      onTap: () async {
        Navigator.of(context).push(ToolWritingDetailPage.route(recod.id ?? 0));
      },
      child: _itemWidget(recod),
    );
  }

  Container _itemWidget(Recods recod) {
    return Container(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.fromLTRB(24.w, 0, 24.w, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                recod.createTime ?? '',
                style: style_3_24,
              ),
            ],
          ),
          SizedBox(
            height: 18.w,
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  recod.originalText ?? '',
                  style: style_1_24,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(
            height: 18.w,
          ),
          Row(
            children: [
              Text(
                '查看详情',
                style: style_2_24,
              ),
              SizedBox(
                width: 10.w,
              ),
              Image.asset(
                "images/arrow_right_black.png",
                width: 10.w,
                height: 18.w,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showPicker(BuildContext context, ToolUserWritingPageProvider provider) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text(
                '拍照',
                textAlign: TextAlign.center,
              ),
              onTap: () {
                _takePhoto(context, provider);
              },
            ),
            ListTile(
              title: const Text(
                '相册',
                textAlign: TextAlign.center,
              ),
              onTap: () {
                _openGallery(context, provider);
              },
            ),
            const SizedBox(height: 8),
            ListTile(
              title: const Text(
                '取消',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              onTap: () {
                Navigator.pop(context); // 关闭底部模态表
              },
            ),
          ],
        );
      },
    );
  }

  _takePhoto(BuildContext context, ToolUserWritingPageProvider provider) async {
    Navigator.pop(context); // 关闭底部模态表

    XFile? pickedFile = await picker.pickImage(
        source: ImageSource.camera, maxHeight: 600, maxWidth: 600);
    provider.tempPictureFile = pickedFile!.path;
    if (pickedFile != null) {
      inputTextController.text = await provider.ocr(context);
      setState(() {
        _textLength = inputTextController.text.length;
      });
    }
  }

  //打开文件夹
  _openGallery(
      BuildContext context, ToolUserWritingPageProvider provider) async {
    Navigator.pop(context); // 关闭底部模态表

    // 检查相册权限
    bool hasPermission = await PermissionDialogUtil.checkAndRequestPhotoPermission(context);
    if (!hasPermission) {
      return;
    }

    XFile? pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 20,
        requestFullMetadata: false);

    if (pickedFile != null) {
      provider.tempPictureFile = pickedFile.path;
      inputTextController.text = await provider.ocr(context);
      setState(() {
        _textLength = inputTextController.text.length;
      });
    }
  }
}
