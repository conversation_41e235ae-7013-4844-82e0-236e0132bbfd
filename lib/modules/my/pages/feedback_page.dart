import 'package:flutter/material.dart';
import 'package:flutter_app_kouyu/common/http/api.dart';
import 'package:flutter_app_kouyu/common/http/models/file_upload_model/file_upload_model.dart';
import 'package:flutter_app_kouyu/common/utils/permission_dialog_util.dart';
import 'package:flutter_app_kouyu/widgets/custome_appbar.dart';
import 'package:flutter_app_kouyu/widgets/custome_scaffold.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class FeedbackPage extends StatefulWidget {
  const FeedbackPage({super.key});
  static Route<void> route() {
    return MaterialPageRoute<void>(builder: (_) => const FeedbackPage());
  }

  @override
  State<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {
  final ImagePicker picker = ImagePicker();

  int type = 1;
  String? feedback;
  String? phone;
  List<String> imageList = [];
  @override
  Widget build(BuildContext context) {
    return CustomeScaffold(
      appbarBuilder: (alpha) {
        return AppBar(
          leadingWidth: 300,
          backgroundColor: alpha > 0 ? Colors.white : const Color(0xFFF3FBFD),
          leading: CustomAppbar.leftWidget(context, text: "举报/反馈"),
        );
      },
      backgroundColor: const Color(0xFFF0F8FA),
      body: Padding(
        padding: EdgeInsets.fromLTRB(40.w, 0, 40.w, 128.w),
        child: ListView(
          children: [
            SizedBox(
              height: 40.w,
            ),
            const Text(
              "问题类型",
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w800,
                  color: Color(0xFF061B1F)),
            ),
            SizedBox(
              height: 24.w,
            ),
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(12)),
              child: Container(
                color: Colors.white,
                padding: EdgeInsets.all(32.w),
                child: Column(
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        setState(() {
                          type = 1;
                        });
                      },
                      child: Row(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "功能异常",
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w800,
                                    color: Color(0xFF061B1F)),
                              ),
                              SizedBox(
                                height: 4.w,
                              ),
                              const Text(
                                "内容报错、卡顿、错位等",
                                style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF646E70)),
                              ),
                            ],
                          ),
                          const Spacer(),
                          type == 1
                              ? Icon(Icons.check_circle,
                                  color: const Color(0xFF4EC0FF), size: 30.w)
                              : Container(
                                  width: 30.w,
                                  height: 30.w,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15.w),
                                      border: Border.all(
                                          width: 3.w,
                                          color: const Color((0xFFDDEDF0)))),
                                ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        setState(() {
                          type = 2;
                        });
                      },
                      child: Row(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "产品改进",
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w800,
                                    color: Color(0xFF061B1F)),
                              ),
                              SizedBox(
                                height: 4.w,
                              ),
                              const Text(
                                "反馈产品及服务优化建议",
                                style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF646E70)),
                              ),
                            ],
                          ),
                          const Spacer(),
                          type == 2
                              ? Icon(Icons.check_circle,
                                  color: const Color(0xFF4EC0FF), size: 30.w)
                              : Container(
                                  width: 30.w,
                                  height: 30.w,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15.w),
                                      border: Border.all(
                                          width: 3.w,
                                          color: const Color((0xFFDDEDF0)))),
                                ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        setState(() {
                          type = 3;
                        });
                      },
                      child: Row(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                "举报违规内容",
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w800,
                                    color: Color(0xFF061B1F)),
                              ),
                              SizedBox(
                                height: 4.w,
                              ),
                              const Text(
                                "请在下方详细描述举报类型和举报内容",
                                style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF646E70)),
                              ),
                              SizedBox(
                                height: 4.w,
                              ),
                              const Text(
                                "举报邮箱：<EMAIL>",
                                style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: Color(0xFF646E70)),
                              ),
                            ],
                          ),
                          const Spacer(),
                          type == 3
                              ? Icon(Icons.check_circle,
                                  color: const Color(0xFF4EC0FF), size: 30.w)
                              : Container(
                                  width: 30.w,
                                  height: 30.w,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15.w),
                                      border: Border.all(
                                          width: 3.w,
                                          color: const Color((0xFFDDEDF0)))),
                                ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 40.w,
            ),
            Row(
              children: [
                Text(
                  "举报/反馈描述",
                  style: TextStyle(
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w800,
                      color: const Color(0xFF061B1F)),
                ),
                Text(
                  " *",
                  style: TextStyle(
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w800,
                      color: Colors.red),
                ),
              ],
            ),
            SizedBox(
              height: 24.w,
            ),
            ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(24.w)),
              child: Container(
                color: Colors.white,
                padding: EdgeInsets.only(left: 32.w, right: 32.w, bottom: 32.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                        height: 200.w,
                        child: TextField(
                            onChanged: (value) {
                              feedback = value;
                            },
                            maxLength: 200,
                            maxLines: 10,
                            decoration: const InputDecoration(
                                hintText: "请输入您的建议或问题或违规情况，以便我们提供更好的服务",
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                hintStyle: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                    color: Color(0xFF8BA2A6))))),
                    SizedBox(
                      height: 24.w,
                    ),
                    Row(
                      children: [
                        for (var i = 0; i < imageList.length && i < 3; i++)
                          Padding(
                            padding: const EdgeInsets.only(right: 5.0),
                            child: Image.network(
                              imageList[i],
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            ),
                          ),
                      ],
                    ),
                    SizedBox(
                      height: 10.w,
                    ),
                    Row(
                      children: [
                        for (var i = 0; i < imageList.length - 3 && i < 3; i++)
                          Padding(
                            padding: const EdgeInsets.only(right: 5.0),
                            child: Image.network(
                              imageList[i + 3],
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                            ),
                          ),
                      ],
                    ),
                    SizedBox(
                      height: 24.w,
                    ),
                    GestureDetector(
                      onTap: () {
                        if (imageList.length >= 6) {
                          EasyLoading.showError("最多添加6张");
                          return;
                        }
                        _showPicker(context);
                      },
                      child: Image.asset(
                        "images/feedback_add_pic.png",
                        width: 80,
                        height: 80,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 40.w,
            ),
            Text(
              "联系方式（选填）",
              style: TextStyle(
                  fontSize: 36.sp,
                  fontWeight: FontWeight.w800,
                  color: Color(0xFF061B1F)),
            ),
            SizedBox(
              height: 24.w,
            ),
            ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(24.w)),
              child: Container(
                color: Colors.white,
                padding: EdgeInsets.only(left: 32.w, right: 32.w),
                child: Column(
                  children: [
                    SizedBox(
                        height: 54,
                        child: TextField(
                            textAlignVertical: TextAlignVertical.top,
                            onChanged: (value) {
                              phone = value;
                            },
                            decoration: const InputDecoration(
                                hintText: "手机/邮箱（用于反馈处理结果）",
                                border: InputBorder.none,
                                enabledBorder: InputBorder.none,
                                focusedBorder: InputBorder.none,
                                hintStyle: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                    color: Color(0xFF8BA2A6))))),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 126.w,
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 20),
        child: Container(
          height: 52,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF98ECEC),
                  Color(0xFF84E9FF),
                  Color(0xFF9BE1FF)
                ]),
          ),
          child: TextButton(
              onPressed: () {
                Api.feedback({
                  "type": type,
                  "feedback": feedback,
                  "mobile": phone,
                  "images": imageList.isNotEmpty ? imageList : null
                }).then((value) {
                  EasyLoading.showSuccess("提交成功");
                  Navigator.pop(context);
                });
              },
              style: const ButtonStyle(
                textStyle: MaterialStatePropertyAll(
                    TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
              ),
              child: const Center(
                child: Text("提交",
                    style: TextStyle(
                        color: Color(0xFF061B1F),
                        fontSize: 18,
                        fontWeight: FontWeight.w800)),
              )),
        ),
      ),
    );
  }

  void _showPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text(
                '拍照',
                textAlign: TextAlign.center,
              ),
              onTap: () {
                _takePhoto();
              },
            ),
            ListTile(
              title: const Text(
                '相册',
                textAlign: TextAlign.center,
              ),
              onTap: () {
                _openGallery();
              },
            ),
            const SizedBox(height: 8),
            ListTile(
              title: const Text(
                '取消',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              onTap: () {
                Navigator.pop(context); // 关闭底部模态表
              },
            ),
          ],
        );
      },
    );
  }

  /// 获取/判断权限
  Future<bool> _checkCameraPermission() async {
    final status = await Permission.camera.status;
    if (!status.isGranted) {
      // 无权限，则请求权限
      final agree = await PermissionDialogUtil.show(context, [
        PermissionData(
            type: PermissionType.camera,
            description: "用于反馈您的问题或建议。开启后，您可拍照上传图片。"),
      ]);
      if (agree != true) {
        return false;
      }
      PermissionStatus requestStatus = await Permission.camera.request();
      return requestStatus == PermissionStatus.granted;
    } else {
      return true;
    }
  }



//拍照
  _takePhoto() async {
    Navigator.pop(context); // 关闭底部模态表
    bool havePermission = await _checkCameraPermission();
    if (!havePermission) {
      EasyLoading.showToast('请开启您的相机权限');
      return;
    }
    XFile? pickedFile = await picker.pickImage(
        source: ImageSource.camera, maxHeight: 600, maxWidth: 600);

    if (pickedFile != null) {
      FileUploadModel model = await Api.userUploadFile(pickedFile.path);
      setState(() {
        imageList.add(model.data!.fileUrl!);
      });
    }
  }

  //打开文件夹
  _openGallery() async {
    Navigator.pop(context); // 关闭底部模态表
    bool havePermission = await _checkPhotosPermission();
    if (!havePermission) {
      EasyLoading.showToast('请开启您的相册权限');
      return;
    }
    XFile? pickedFile = await picker.pickImage(
        source: ImageSource.gallery, maxHeight: 600, maxWidth: 600);
    if (pickedFile != null) {
      FileUploadModel model = await Api.userUploadFile(pickedFile.path);
      setState(() {
        imageList.add(model.data!.fileUrl!);
      });
    }
  }
}
